import os
from abc import ABC, abstractmethod
from contextlib import contextmanager
from pathlib import Path
from typing import Any, Dict, List, Optional

import numpy as np
import open3d as o3d

from data.datum import Instance, PickPose, Pose, Scene


class CameraModule(ABC):
    def __init__(self, config: Dict[str, Any], logger):
        self.config = config
        self.logger = logger
        self.visualize_enable = config["visualization"]["enabled"]
        self.visualize_config = config["visualization"]

    @abstractmethod
    def capture_scene(self) -> Scene:
        """采集场景数据"""
        pass

    @contextmanager
    def controller(self):
        """
        上下文管理器，用于安全地连接和断开相机
        示例用法:
        with camera.controller() as cam:
            scene = cam.capture_scene()
        """
        pass

    def visualize(self, scene: Scene) -> None:
        output_dir = os.path.join(self.visualize_config["save_path"], scene.timestamp)
        Path(output_dir).mkdir(exist_ok=True, parents=True)
        scene.save_data(
            output_dir,
            save_image=self.visualize_config["save_image"],
            save_depth=self.visualize_config["save_depth"],
            save_pointcloud=self.visualize_config["save_pointcloud"],
        )
        if self.logger:
            self.logger.info(f"场景数据已保存到: {output_dir}")
