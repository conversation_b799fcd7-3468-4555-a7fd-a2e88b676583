"""
https://doc.percipio.xyz/cam/latest/apiguides/api_description_python.html
"""

import datetime
import os
import sys
from contextlib import contextmanager
from pathlib import Path
from typing import Any, Dict, Optional

import cv2
import numpy as np
import open3d as o3d

from data.datum import Instance, PickP<PERSON>, Pose, Scene

from .BasicCamera import CameraModule

if "3.8" in sys.version:
    from .pcammls_py38 import pcammls
    from .pcammls_py38.pcammls import (
        PERCIPIO_STREAM_COLOR,
        PERCIPIO_STREAM_DEPTH,
        TY_ENUM_ENTRY,
        TY_EVENT_DEVICE_OFFLINE,
        PercipioSDK,
        image_data,
        pointcloud_data_list,
    )
elif "3.9" in sys.version:
    from .pcammls_py39 import pcammls
    from .pcammls_py39.pcammls import (
        PERCIPIO_STREAM_COLOR,
        PERCIPIO_STREAM_DEPTH,
        TY_ENUM_ENTRY,
        TY_EVENT_DEVICE_OFFLINE,
        PercipioSDK,
        image_data,
        pointcloud_data_list,
    )
else:
    raise NotImplementedError(
        "TUYANG cam got a not support python version, plz build from pcammls_src"
    )


class PythonPercipioDeviceEvent(pcammls.DeviceEvent):
    Offline = False

    def __init__(self):
        pcammls.DeviceEvent.__init__(self)

    def run(self, handle, eventID):
        if eventID == TY_EVENT_DEVICE_OFFLINE:
            print("=== Event Callback: Device Offline!")
            self.Offline = True
        return 0

    def IsOffline(self):
        return self.Offline


class PercipioCamera(CameraModule):
    def __init__(self, config: Dict[str, Any], logger):
        super().__init__(config, logger)

        self.sdk = None
        self.handle = None
        self.event = None
        self.depth_intrinsics = None
        self.color_intrinsics = None
        self.depth_scale_unit = None
        self.device_sn = config.get("device_sn", "")
        self.selected_idx = config.get("device_index", -1)

        self.image_mode = config.get("image_mode", 0)
        self.depth_mode = config.get("depth_mode", 0)
        self.image_undistort = config.get("image_undistort", True)
        self.trigger_mode = config.get("trigger_mode", True)
        # self.pointcloud_mode = config.get("pointcloud_mode", 0)

        # 输出
        self.rgb_image = image_data()
        self.img_undistortion_color = image_data()  # 去畸变
        self.img_registration_color =  image_data()  # 对齐深度
        self.depth_render = image_data()
        self.pointcloud = pointcloud_data_list()

    @contextmanager
    def controller(self):
        """上下文管理器，用于安全地连接和断开相机"""
        try:
            self._initialize_camera()
            yield self
        except Exception as e:
            self.logger.error(f"Camera error: {str(e)}")
        finally:
            self._release_camera()

    def capture_scene(self) -> Scene:
        """采集场景数据"""
        if not self.handle:
            raise RuntimeError("Camera not initialized")

        # 读取相机的传送的数据
        # trigger mode
        if self.trigger_mode:
            self.sdk.DeviceControlTriggerModeSendTriggerSignal(self.handle)
            image_list = self.sdk.DeviceStreamRead(self.handle, 20000)
        else:
            image_list = self.sdk.DeviceStreamRead(self.handle, -1)
        len(image_list)
        color_frame = None
        depth_frame = None

        for frame in image_list:
            if frame.streamID == PERCIPIO_STREAM_DEPTH:
                depth_frame = frame
            elif frame.streamID == PERCIPIO_STREAM_COLOR:
                color_frame = frame

        if not color_frame or not depth_frame:
            raise RuntimeError(
                f"Failed to capture both color and depth frames, color_frame: {str(color_frame)}, depth_frame: {str(depth_frame)}"
            )

        # 处理深度帧
        self.sdk.DeviceStreamDepthRender(depth_frame, self.depth_render)
        depth_arr = depth_frame.as_nparray()[:, :, 0].astype(np.float32) / 1000.0
        # depth_image = self.depth_render.as_nparray()

        # 处理彩色帧
        self.sdk.DeviceStreamImageDecode(color_frame, self.rgb_image)
        # 彩色帧去畸变
        self.sdk.DeviceStreamDoUndistortion(
            self.color_calib, self.rgb_image, self.img_undistortion_color
        )
        # 彩色帧对齐深度图(去外参)
        self.sdk.DeviceStreamMapRGBImageToDepthCoordinate(
            self.depth_calib,
            depth_frame,
            self.depth_scale_unit,
            self.color_calib,
            self.img_undistortion_color,
            self.img_registration_color,
        )
        if self.image_undistort:
            color_arr = self.img_registration_color.as_nparray()
        else:
            color_arr = self.rgb_image.as_nparray()
        # color_arr = cv2.cvtColor(color_arr, cv2.COLOR_BGR2RGB)

        # 生成点云
        self.sdk.DeviceStreamMapDepthImageToPoint3D(
            depth_frame, self.depth_calib, self.depth_scale_unit, self.pointcloud
        )
        sz = self.pointcloud.size()
        print("get p3d size : {}".format(sz))
        center = depth_frame.width * depth_frame.height / 2 + depth_frame.width / 2

        # show p3d arr data
        p3d_nparray = self.pointcloud.as_nparray()
        # p3d = self.pointcloud.get_vaDeviceStreamMapRGBImageToDepthCoordinatelue(int(center))

        p3d_nparray = (
            np.where(np.isnan(p3d_nparray), 0, p3d_nparray).reshape((-1, 3)) / 1000
        )  # meter
        point_cloud = o3d.geometry.PointCloud()
        point_cloud.points = o3d.utility.Vector3dVector(p3d_nparray)
        # point_cloud.colors = o3d.utility.Vector3dVector(colors)

        return Scene(
            camera_id=self.selected_idx,
            pointcloud=point_cloud,
            color_image=color_arr,
            depth_image=depth_arr,
            depth_intrinsics=self.depth_intrinsics,
            resolution=np.array([color_arr.shape[1], color_arr.shape[0]]),
        )

    def visualize(self, scene: Scene):
        output_dir = os.path.join(self.visualize_config["save_path"], scene.timestamp)
        Path(output_dir).mkdir(exist_ok=True, parents=True)
        scene.save_data(
            output_dir,
            save_image=self.visualize_config["save_image"],
            save_depth=self.visualize_config["save_depth"],
            save_pointcloud=self.visualize_config["save_pointcloud"],
        )
        if self.logger:
            self.logger.info(f"场景数据已保存到: {output_dir}")

    def _initialize_camera(self):
        """初始化相机设备"""
        self.sdk = PercipioSDK()

        # 设备发现与选择
        dev_list = self.sdk.ListDevice()
        if not dev_list:
            raise RuntimeError("No devices found")

        if self.device_sn:
            self.selected_idx = next(
                (idx for idx, dev in enumerate(dev_list) if dev.id == self.device_sn),
                -1,
            )

        if self.selected_idx < 0 or self.selected_idx >= len(dev_list):
            self.selected_idx = 0

        sn = dev_list[self.selected_idx].id
        self.handle = self.sdk.Open(sn)

        if not self.sdk.isValidHandle(self.handle):
            err = self.sdk.TYGetLastErrorCodedescription()
            raise RuntimeError(f"Device open failed: {err}")

        # 配置颜色流
        color_fmt_list = self.sdk.DeviceStreamFormatDump(
            self.handle, PERCIPIO_STREAM_COLOR
        )
        if color_fmt_list:

            print("color image format list:")
            for idx in range(len(color_fmt_list)):
                fmt = color_fmt_list[idx]
                print(
                    "\t{} -size[{}x{}]\t-\t desc:{}".format(
                        idx, self.sdk.Width(fmt), self.sdk.Height(fmt), fmt.getDesc()
                    )
                )
            print("\tSelect {}".format(fmt.getDesc()))

            self.sdk.DeviceStreamFormatConfig(
                self.handle, PERCIPIO_STREAM_COLOR, color_fmt_list[self.image_mode]
            )

            color_enum_desc = TY_ENUM_ENTRY()
            self.sdk.DeviceReadCurrentEnumData(
                self.handle, PERCIPIO_STREAM_COLOR, color_enum_desc
            )
            print(
                "current color image mode  {}x{}".format(
                    self.sdk.Width(color_enum_desc), self.sdk.Height(color_enum_desc)
                )
            )

            self.color_calib = self.sdk.DeviceReadCalibData(
                self.handle, PERCIPIO_STREAM_COLOR
            )
            # add
            color_calib_width = self.color_calib.Width()
            color_calib_height = self.color_calib.Height()
            color_calib_intr = self.color_calib.Intrinsic()
            color_calib_extr = self.color_calib.Extrinsic()
            color_calib_dis = self.color_calib.Distortion()
            print("color calib info:")
            print(
                "\tcalib size       :[{}x{}]".format(
                    color_calib_width, color_calib_height
                )
            )
            print("\tcalib intr       : {}".format(color_calib_intr))
            print("\tcalib extr       : {}".format(color_calib_extr))
            print("\tcalib distortion : {}".format(color_calib_dis))

            self.color_intrinsics = self._parse_intrinsics(self.color_calib)

        # 配置深度流
        depth_fmt_list = self.sdk.DeviceStreamFormatDump(
            self.handle, PERCIPIO_STREAM_DEPTH
        )
        if depth_fmt_list:

            print("depth image format list:")
            for idx in range(len(depth_fmt_list)):
                fmt = depth_fmt_list[idx]
                print(
                    "\t{} -size[{}x{}]\t-\t desc:{}".format(
                        idx, self.sdk.Width(fmt), self.sdk.Height(fmt), fmt.getDesc()
                    )
                )
            print("\tSelect {}".format(fmt.getDesc()))

            self.sdk.DeviceStreamFormatConfig(
                self.handle, PERCIPIO_STREAM_DEPTH, depth_fmt_list[self.depth_mode]
            )

            depth_enum_desc = TY_ENUM_ENTRY()
            self.sdk.DeviceReadCurrentEnumData(
                self.handle, PERCIPIO_STREAM_DEPTH, depth_enum_desc
            )
            print(
                "current depth image mode  {}x{}".format(
                    self.sdk.Width(depth_enum_desc), self.sdk.Height(depth_enum_desc)
                )
            )

            self.depth_calib = self.sdk.DeviceReadCalibData(
                self.handle, PERCIPIO_STREAM_DEPTH
            )

            depth_calib_width = self.depth_calib.Width()
            depth_calib_height = self.depth_calib.Height()
            depth_calib_intr = self.depth_calib.Intrinsic()
            depth_calib_extr = self.depth_calib.Extrinsic()
            depth_calib_dis = self.depth_calib.Distortion()
            print("depth calib info:")
            print(
                "\tcalib size       :[{}x{}]".format(
                    depth_calib_width, depth_calib_height
                )
            )
            print("\tcalib intr       : {}".format(depth_calib_intr))
            print("\tcalib extr       : {}".format(depth_calib_extr))
            print("\tcalib distortion : {}".format(depth_calib_dis))

            self.depth_intrinsics = self._parse_intrinsics(self.depth_calib)

        # 点云
        # print ('depth image format list:')
        # for idx in range(len(depth_fmt_list)):
        #     fmt = depth_fmt_list[idx]
        #     print ('\t{} -size[{}x{}]\t-\t desc:{}'.format(idx, self.sdk.Width(fmt), self.sdk.Height(fmt), fmt.getDesc()))
        # self.sdk.DeviceStreamFormatConfig(self.handle, PERCIPIO_STREAM_DEPTH, depth_fmt_list[self.pointcloud_mode])

        err = self.sdk.DeviceLoadDefaultParameters(self.handle)
        if err:
            print("Load default parameters fail: ", end="")
            print(self.sdk.TYGetLastErrorCodedescription())
        else:
            print("Load default parameters successful")

        self.depth_scale_unit = self.sdk.DeviceReadCalibDepthScaleUnit(self.handle)
        print("depth image scale unit :{}".format(self.depth_scale_unit))

        # # trigger mode
        if self.trigger_mode:
            self.sdk.DeviceControlTriggerModeEnable(self.handle, 1)
        # 加载默认参数并启用流
        if self.sdk.DeviceLoadDefaultParameters(self.handle):
            self.logger.warning("Load default parameters failed")

        err = self.sdk.DeviceStreamEnable(
            self.handle, PERCIPIO_STREAM_COLOR | PERCIPIO_STREAM_DEPTH
        )
        if err:
            raise RuntimeError(f"Stream enable failed: {err}")

        # 注册回调并启动流
        self.event = PythonPercipioDeviceEvent()
        self.sdk.DeviceRegiststerCallBackEvent(self.event)
        self.sdk.DeviceStreamOn(self.handle)

        self.logger.info(f"Camera {sn} initialized successfully")

    def _release_camera(self):
        """释放相机资源"""
        if self.handle:
            self.sdk.DeviceStreamOff(self.handle)
            self.sdk.Close(self.handle)
            self.handle = None
            self.logger.info("Camera resources released")

    def _parse_intrinsics(self, calib_data: Any) -> np.ndarray:
        """从校准数据中解析内参矩阵"""
        intr = calib_data.Intrinsic()
        return np.array(
            [
                [intr[0]],
                [intr[1]],
                [intr[2]],
                [intr[3]],
                [intr[4]],
                [intr[5]],
                [intr[6]],
                [intr[7]],
                [intr[8]],
            ]
        ).reshape(3, 3)

if __name__ == "__main__":
    # python -m connect.camera.PercipioCamera
    import yaml
    import logging

    # 加载配置
    with open("./cfg/camera/PercipioCamera.yaml", "r") as f:
        config = yaml.safe_load(f)

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    logging.getLogger().setLevel(logging.INFO)
    logger = logging.getLogger("PickPipeline")

    # 创建相机实例
    camera_config = config["camera"]["params"]
    camera = PercipioCamera(camera_config, logger)

    # 使用上下文管理器捕获场景
    with camera.controller() as cam:
        scene = cam.capture_scene()

    camera.visualize(scene)

    # 使用场景数据
    print(f"点云点数: {len(scene.pointcloud.points)}")
    print(f"彩色图尺寸: {scene.color_image.shape}")
    print(f"深度图尺寸: {scene.depth_image.shape}")
    print(f"相机内参: \n{scene.depth_intrinsics}")

    # 关闭相机（或使用with语句自动管理）
