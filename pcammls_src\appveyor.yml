image: 
  - Visual Studio 2017
  - Ubuntu

before_build:
  - pip install numpy

on_success:
  - echo success
on_failure:
  - echo failed


for:
  - 
    matrix:
      only:
        - image: Ubuntu
    install:
      - echo start install
      - sudo apt-get -y install libpcre3-dev python-dev python3.8-dev
      - curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
      - python3.8 get-pip.py
      - python3.8 -m pip --version
      - pip3.8 install numpy
      - wget http://prdownloads.sourceforge.net/swig/swig-4.0.0.tar.gz
      - tar -zxf swig-4.0.0.tar.gz
      - cd swig-4.0.0
      - ./configure
      - make -j4
      - sudo make install
      - cd ..

    build_script:
      - mkdir build
      - cd build
      - cmake .. -DCMAKE_INSTALL_PREFIX=./install/ -DCAMPORT_ARCH=x64

    artifacts:
      - path: build/install/
        name: linux_x64_bin

    
  - 
    matrix:
      only:
        - image: Visual Studio 2017
    init:
      - git config --global core.autocrlf input
    install:
      - echo start install
      - cinst swig --version 4.0.0
    build_script:
      - mkdir build
      - cd build
      - cmake .. -G "Visual Studio 15 2017 Win64" -DCMAKE_INSTALL_PREFIX=../install/ -DSWIG_EXECUTABLE=C:/ProgramData/chocolatey/lib/swig/tools/install/swigwin-4.0.0/swig.exe -DPYTHON_LIBRARY=C:/Python37-x64/libs/python37.lib -DPYTHON_INCLUDE_DIR=C:/Python37-x64/include -DPYTHON_VERSION=3.7

      - cmake --build . --config Release --target install
      - move ../install/PYTHON ../install/PYTHON37
      - del /q/s *
      - cmake .. -G "Visual Studio 15 2017 Win64" -DCMAKE_INSTALL_PREFIX=../install/ -DSWIG_EXECUTABLE=C:/ProgramData/chocolatey/lib/swig/tools/install/swigwin-4.0.0/swig.exe -DPYTHON_LIBRARY=C:/Python36-x64/libs/python36.lib -DPYTHON_INCLUDE_DIR=C:/Python36-x64/include -DPYTHON_VERSION=3.6
      - cmake --build . --config Release --target install
      - move ../install/PYTHON ../install/PYTHON36
      - del /q/s *
      - cmake .. -G "Visual Studio 15 2017 Win64" -DTARGET_LANGUAGE=CSHARP -DCMAKE_INSTALL_PREFIX=../install/ -DSWIG_EXECUTABLE=C:/ProgramData/chocolatey/lib/swig/tools/install/swigwin-4.0.0/swig.exe
      - cmake --build . --config Release --target install
    
    artifacts:
      - path: ./install/
        name: win_x64_bin
